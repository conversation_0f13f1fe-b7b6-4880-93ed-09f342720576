"""
نظام طباعة متقدم للفواتير مع رسم مباشر بالألوان
"""

import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton, 
                             QLabel, QRadioButton, QButtonGroup, QMessageBox,
                             QFrame, QScrollArea, QWidget)
from PyQt5.QtCore import Qt, pyqtSignal, QRect
from PyQt5.QtGui import (QFont, QIcon, QPainter, QPen, QBrush, QColor, 
                         QLinearGradient, QPixmap, QFontMetrics, QImage)
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
from sqlalchemy.orm import sessionmaker
from database.models import Transaction, Customer, TransactionItem, Product, Supplier, TransactionType
from utils.company_settings import get_company_settings
from utils.currency_formatter import format_currency, format_number
from main import resource_path
import json
import base64
import sys

class AdvancedInvoicePrinter(QDialog):
    def __init__(self, engine, invoice_id, parent=None):
        super().__init__(parent)
        self.engine = engine
        self.invoice_id = invoice_id
        self.invoice_data = None
        self.printer_type = "a4"  # افتراضي A4
        self.zoom_factor = 0.4  # عامل التكبير الأولي - أصغر لرؤية كل التفاصيل في المعاينة
        self.is_preview_mode = True  # وضع المعاينة
        
        self.setWindowTitle("طباعة الفاتورة - تصميم متقدم")
        self.setGeometry(50, 50, 1400, 1000)  # نافذة أكبر لعرض A4 بوضوح
        self.setModal(True)
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint | Qt.WindowMinMaxButtonsHint)
        
        self.setup_ui()
        self.load_invoice_data()
        self.update_preview()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # عنوان النافذة
        title_label = QLabel("🎨 طباعة الفاتورة - تصميم متقدم")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                font-size: 20px;
                font-weight: bold;
                padding: 15px;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title_label)
        
        # إعدادات الطابعة
        printer_frame = QFrame()
        printer_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 2px solid #DEE2E6;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
        """)
        printer_layout = QHBoxLayout()
        printer_frame.setLayout(printer_layout)
        
        printer_label = QLabel("🖨️ نوع الطابعة:")
        printer_label.setFont(QFont("Arial", 12, QFont.Bold))
        printer_layout.addWidget(printer_label)
        
        self.printer_group = QButtonGroup()
        
        self.a4_radio = QRadioButton("📄 طابعة A4 (210×297مم)")
        self.a4_radio.setChecked(True)
        self.a4_radio.toggled.connect(self.on_printer_type_changed)
        self.printer_group.addButton(self.a4_radio)
        printer_layout.addWidget(self.a4_radio)
        
        self.roll_radio = QRadioButton("📜 طابعة رول (80مم)")
        self.roll_radio.toggled.connect(self.on_printer_type_changed)
        self.printer_group.addButton(self.roll_radio)
        printer_layout.addWidget(self.roll_radio)
        
        # إضافة معلومات أبعاد المعاينة
        preview_info = QLabel("📐 المعاينة بأبعاد A4 الحقيقية - وضع طولي (Portrait) 210×297مم - استخدم Ctrl+عجلة الماوس للتكبير/التصغير")
        preview_info.setStyleSheet("""
            QLabel {
                color: #6C757D;
                font-size: 11px;
                padding: 5px;
                background-color: #F8F9FA;
                border-radius: 4px;
                margin: 2px;
            }
        """)
        preview_info.setAlignment(Qt.AlignCenter)

        printer_layout.addStretch()
        layout.addWidget(printer_frame)
        layout.addWidget(preview_info)

        # منطقة المعاينة
        self.preview_scroll = QScrollArea()
        self.preview_widget = QWidget()
        self.preview_scroll.setWidget(self.preview_widget)
        self.preview_scroll.setWidgetResizable(True)
        self.preview_scroll.setStyleSheet("""
            QScrollArea {
                border: 2px solid #DEE2E6;
                border-radius: 8px;
                background-color: white;
            }
        """)
        layout.addWidget(self.preview_scroll)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.preview_btn = QPushButton("👁️ معاينة")
        self.preview_btn.setStyleSheet(self.get_button_style("#17A2B8"))
        self.preview_btn.clicked.connect(self.update_preview)
        buttons_layout.addWidget(self.preview_btn)
        
        self.pdf_btn = QPushButton("💾 حفظ PDF")
        self.pdf_btn.setStyleSheet(self.get_button_style("#28A745"))
        self.pdf_btn.clicked.connect(self.save_as_pdf)
        buttons_layout.addWidget(self.pdf_btn)
        
        self.print_btn = QPushButton("🖨️ طباعة")
        self.print_btn.setStyleSheet(self.get_button_style("#007BFF"))
        self.print_btn.clicked.connect(self.print_invoice)
        buttons_layout.addWidget(self.print_btn)
        
        self.close_btn = QPushButton("❌ إغلاق")
        self.close_btn.setStyleSheet(self.get_button_style("#DC3545"))
        self.close_btn.clicked.connect(self.safe_close)
        buttons_layout.addWidget(self.close_btn)
        
        layout.addLayout(buttons_layout)
    
    def get_button_style(self, color):
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                font-size: 24px;
                font-weight: bold;
                padding: 12px 20px;
                border: none;
                border-radius: 6px;
                min-width: 120px;
                margin: 5px;
            }}
            QPushButton:hover {{
                background-color: {color}DD;
            }}
            QPushButton:pressed {{
                background-color: {color}BB;
            }}
        """
    
    def on_printer_type_changed(self):
        if self.a4_radio.isChecked():
            self.printer_type = "a4"
        else:
            self.printer_type = "roll"
        self.update_preview()

    def safe_close(self):
        """إغلاق آمن للنافذة مع تنظيف الذاكرة"""
        try:
            # تنظيف widget المعاينة
            if hasattr(self, 'preview_widget') and self.preview_widget:
                layout = self.preview_widget.layout()
                if layout:
                    while layout.count():
                        child = layout.takeAt(0)
                        if child.widget():
                            widget = child.widget()
                            widget.setParent(None)
                            widget.deleteLater()
                    layout.setParent(None)
                    layout.deleteLater()

            # تنظيف البيانات
            self.invoice_data = None

            # إغلاق النافذة
            self.close()

        except Exception as e:
            print(f"❌ خطأ أثناء الإغلاق الآمن: {str(e)}")
            # إغلاق عادي في حالة الخطأ
            self.close()

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        try:
            # تنظيف الذاكرة عند الإغلاق
            self.safe_close()
            event.accept()
        except Exception as e:
            print(f"❌ خطأ في closeEvent: {str(e)}")
            event.accept()
    
    def load_invoice_data(self):
        """تحميل بيانات الفاتورة أو المرتجع"""
        try:
            Session = sessionmaker(bind=self.engine)
            session = Session()

            # تحميل الفاتورة أو المرتجع
            transaction = session.query(Transaction).filter_by(id=self.invoice_id).first()
            if not transaction:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الفاتورة")
                return

            # تحديد نوع المعاملة
            is_return = transaction.type in [
                TransactionType.SALE_RETURN, 
                TransactionType.PURCHASE_RETURN
            ]
            is_sale_return = transaction.type == TransactionType.SALE_RETURN
            is_purchase_return = (
                transaction.type == TransactionType.PURCHASE_RETURN
            )
            is_purchase = transaction.type == TransactionType.PURCHASE
            is_sale = transaction.type == TransactionType.SALE

            # تحميل العميل أو المورد
            contact_name = "غير محدد"
            if is_sale_return and transaction.customer_id:
                customer = session.query(Customer).filter_by(
                    id=transaction.customer_id
                ).first()
                contact_name = customer.name if customer else "عميل نقدي"
            elif is_purchase_return and transaction.supplier_id:
                supplier = session.query(Supplier).filter_by(
                    id=transaction.supplier_id
                ).first()
                contact_name = supplier.name if supplier else "غير محدد"
            elif is_purchase and transaction.supplier_id:
                supplier = session.query(Supplier).filter_by(
                    id=transaction.supplier_id
                ).first()
                contact_name = supplier.name if supplier else "غير محدد"
            elif is_sale and transaction.customer_id:
                customer = session.query(Customer).filter_by(
                    id=transaction.customer_id
                ).first()
                contact_name = customer.name if customer else "عميل نقدي"

            # تحميل المنتجات
            items = session.query(TransactionItem, Product).join(
                Product
            ).filter(
                TransactionItem.transaction_id == self.invoice_id
            ).all()

            items_data = []
            for item, product in items:
                # حساب الإجمالي
                total_price = float(item.quantity) * float(item.price)
                items_data.append({
                    'name': product.name or 'غير محدد',
                    'quantity': float(item.quantity or 0),
                    'price': float(item.price or 0),
                    'total': total_price,
                    'unit': product.unit or 'قطعة'
                })

            # تحديد عنوان الفاتورة
            if is_sale_return:
                invoice_title = "فاتورة مرتجع مبيعات"
                invoice_type = "مرتجع مبيعات"
            elif is_purchase_return:
                invoice_title = "فاتورة مرتجع مشتريات"
                invoice_type = "مرتجع مشتريات"
            elif is_purchase:
                invoice_title = "فاتورة مشتريات"
                invoice_type = "مشتريات"
            else:
                invoice_title = "فاتورة مبيعات"
                invoice_type = "مبيعات"

            # معلومات الفاتورة الأصلية للمرتجعات
            original_invoice_id = None
            if is_return and transaction.original_transaction_id:
                original_invoice_id = transaction.original_transaction_id

            self.invoice_data = {
                'id': transaction.id,
                'date': (
                    transaction.date.strftime("%Y-%m-%d") 
                    if transaction.date else "غير محدد"
                ),
                'customer_name': contact_name,
                'items': items_data,
                'total_amount': float(transaction.total_amount or 0),
                'paid_amount': float(transaction.paid_amount or 0),
                'remaining_amount': float(
                    (transaction.total_amount or 0) - 
                    (transaction.paid_amount or 0)
                ),
                'discount': float(transaction.discount or 0),
                'is_return': is_return,
                'is_sale_return': is_sale_return,
                'is_purchase_return': is_purchase_return,
                'invoice_title': invoice_title,
                'invoice_type': invoice_type,
                'original_invoice_id': original_invoice_id
            }

            session.close()

        except Exception as e:
            QMessageBox.critical(
                self, 
                "خطأ", 
                f"حدث خطأ أثناء تحميل بيانات الفاتورة:\n{str(e)}"
            )
    
    def wheelEvent(self, event):
        """التحكم في الزوم باستخدام Ctrl + عجلة الماوس مع حماية من الأخطاء"""
        try:
            if event.modifiers() == Qt.ControlModifier:
                # حساب الزوم بخطوات أصغر لتجنب القفزات الكبيرة
                delta = event.angleDelta().y()
                old_zoom = self.zoom_factor

                if delta > 0:
                    self.zoom_factor *= 1.05  # تكبير بخطوات أصغر
                else:
                    self.zoom_factor /= 1.05  # تصغير بخطوات أصغر

                # تحديد حدود آمنة للزوم
                self.zoom_factor = max(0.3, min(self.zoom_factor, 3.0))

                # التحقق من تغيير عامل التكبير
                if abs(self.zoom_factor - old_zoom) > 0.01:
                    # تحديث المعاينة مع معالجة الأخطاء
                    try:
                        self.update_preview()
                    except Exception as e:
                        print(f"❌ خطأ في تحديث المعاينة أثناء الزوم: {str(e)}")
                        # إعادة تعيين عامل التكبير في حالة الخطأ
                        self.zoom_factor = old_zoom

                event.accept()
            else:
                super().wheelEvent(event)

        except Exception as e:
            print(f"❌ خطأ في معالجة حدث العجلة: {str(e)}")
            # إعادة تعيين عامل التكبير إلى القيمة الافتراضية
            self.zoom_factor = 1.0
            
    def update_preview(self):
        """تحديث المعاينة برسم مباشر مع دعم الزوم المحسن"""
        if not self.invoice_data:
            return

        try:
            # استخدام أبعاد A4 الحقيقية للمعاينة المطابقة للطباعة
            if self.printer_type == "a4":
                # أبعاد A4 الحقيقية بنسبة 1:1.414 (210x297mm)
                # استخدام أبعاد أكبر لوضوح أفضل في المعاينة
                base_width, base_height = 1000, 1414  # أبعاد A4 محسنة للمعاينة
            else:
                base_width, base_height = 300, 800

            # تحديد حد أقصى لحجم الصورة لتجنب مشاكل الذاكرة
            max_width = 2400  # حد أقصى للعرض
            max_height = 3300  # حد أقصى للارتفاع

            # تطبيق عامل التكبير مع حدود آمنة
            width = min(int(base_width * self.zoom_factor), max_width)
            height = min(int(base_height * self.zoom_factor), max_height)

            # التأكد من أن الحجم ليس صغيراً جداً
            width = max(width, 200)
            height = max(height, 300)

            # إنشاء الصورة مع معالجة الأخطاء
            pixmap = QPixmap(width, height)
            if pixmap.isNull():
                print(f"⚠️ تحذير: فشل في إنشاء صورة بحجم {width}x{height}")
                return

            pixmap.fill(QColor(255, 255, 255))  # خلفية بيضاء

            painter = QPainter(pixmap)
            if not painter.isActive():
                print("⚠️ تحذير: فشل في تفعيل الرسام")
                return

            # تكبير محتوى الرسم بنسبة التكبير مع عامل تصحيح للمعاينة
            preview_scale_factor = 0.5  # عامل تصحيح للمعاينة - نصف الحجم
            painter.scale(self.zoom_factor * preview_scale_factor, self.zoom_factor * preview_scale_factor)

            self.draw_invoice(painter, base_width, base_height)
            painter.end()

            # تحديث widget المعاينة بطريقة آمنة
            self.update_preview_widget(pixmap)

        except Exception as e:
            print(f"❌ خطأ في تحديث المعاينة: {str(e)}")
            # في حالة الخطأ، إعادة تعيين عامل التكبير
            self.zoom_factor = 1.0

    def update_preview_widget(self, pixmap):
        """تحديث widget المعاينة بطريقة آمنة ومحسنة"""
        try:
            # البحث عن label المعاينة الموجود أو إنشاء واحد جديد
            preview_label = None
            layout = self.preview_widget.layout()

            if layout:
                # البحث عن label موجود
                for i in range(layout.count()):
                    item = layout.itemAt(i)
                    if item and item.widget() and isinstance(item.widget(), QLabel):
                        preview_label = item.widget()
                        break

            # إذا لم يوجد label، إنشاء واحد جديد
            if not preview_label:
                preview_label = QLabel()
                preview_label.setAlignment(Qt.AlignCenter)
                preview_label.setScaledContents(False)  # عدم تمديد المحتوى
                preview_label.setStyleSheet("""
                    QLabel {
                        border: 2px solid #BDC3C7;
                        border-radius: 8px;
                        background-color: white;
                        margin: 10px;
                    }
                """)

                # إنشاء تخطيط إذا لم يكن موجوداً
                if not layout:
                    layout = QVBoxLayout()
                    layout.setContentsMargins(10, 10, 10, 10)
                    self.preview_widget.setLayout(layout)

                layout.addWidget(preview_label)

            # تحديث الصورة
            preview_label.setPixmap(pixmap)

            # إجبار تحديث الواجهة
            self.preview_widget.update()

        except Exception as e:
            print(f"❌ خطأ في تحديث widget المعاينة: {str(e)}")
            # في حالة الخطأ، محاولة إنشاء label جديد بطريقة بسيطة
            try:
                # تنظيف كامل
                for child in self.preview_widget.findChildren(QLabel):
                    child.deleteLater()

                # إنشاء label جديد
                new_label = QLabel()
                new_label.setPixmap(pixmap)
                new_label.setAlignment(Qt.AlignCenter)

                # إنشاء تخطيط جديد
                new_layout = QVBoxLayout()
                new_layout.addWidget(new_label)

                # تطبيق التخطيط
                old_layout = self.preview_widget.layout()
                if old_layout:
                    old_layout.deleteLater()

                self.preview_widget.setLayout(new_layout)

            except Exception as e2:
                print(f"❌ خطأ في الإنشاء البديل: {str(e2)}")
    
    def draw_invoice(self, painter, width, height):
        """رسم الفاتورة بالألوان والتصميم الجميل مع حماية من الأخطاء"""
        try:
            # التحقق من صحة المعاملات
            if not painter or not painter.isActive():
                print("❌ خطأ: الرسام غير نشط")
                return

            if width <= 0 or height <= 0:
                print(f"❌ خطأ: أبعاد غير صحيحة {width}x{height}")
                return

            painter.setRenderHint(QPainter.Antialiasing)

            # التحقق من وجود بيانات الفاتورة
            if not self.invoice_data or not self.invoice_data.get('items'):
                painter.setPen(QPen(QColor(255, 0, 0), 2))
                painter.setFont(QFont("Arial", 16, QFont.Bold))
                painter.drawText(50, height//2, "خطأ: لا توجد بيانات للفاتورة")
                return

        except Exception as e:
            print(f"❌ خطأ في بداية رسم الفاتورة: {str(e)}")
            return

        # تحميل إعدادات الشركة
        company_settings = get_company_settings()
        company_name = company_settings.get(
            "company_name", 
            "هوم سنتر للأدوات المنزلية"
        )
        company_address = company_settings.get("address", "حي الصفا - جدة - المملكة العربية السعودية")
        company_phone = company_settings.get("phone", "01010101010")
        company_email = company_settings.get("email", "<EMAIL>")

        y = 20

        if self.printer_type == "a4":
            self.draw_a4_invoice(
                painter, width, height, company_name, 
                company_address, company_phone, company_email
            )
        else:
            self.draw_roll_invoice(
                painter, width, height, company_name, company_phone
            )
    
    def draw_a4_invoice(self, painter, width, height, company_name, company_address, company_phone, company_email):
        """رسم فاتورة A4 بتصميم جميل مع نصوص واضحة ولوجو مخصص"""
        from utils.company_settings import get_company_settings
        company_settings = get_company_settings()
        y = 30

        # تحديد الأحجام حسب الوضع (معاينة أم طباعة)
        if hasattr(self, 'is_preview_mode') and self.is_preview_mode:
            # استخدام التصميم الجديد للمعاينة
            self.draw_new_preview_design(painter, width, height, company_name, company_address, company_phone, company_email)
            return
        else:
            # أحجام كبيرة للطباعة والحفظ - التصميم القديم
            size_multiplier = 2.0

        # رسم الترويسة بالأبيض والأسود مع الحفاظ على التصميم
        header_height = int(400 * size_multiplier)  # حجم متغير حسب الوضع

        # خلفية بيضاء مع حدود سوداء
        painter.setBrush(QBrush(QColor(255, 255, 255)))  # خلفية بيضاء
        painter.setPen(QPen(QColor(0, 0, 0), int(3 * size_multiplier)))  # حدود متغيرة
        painter.drawRoundedRect(15, y, width - 30, header_height, int(15 * size_multiplier), int(15 * size_multiplier))

        # رسم اللوجو المخصص أو الافتراضي مع حجم أكبر
        logo_x = int(40 * size_multiplier)
        logo_y = y + int(30 * size_multiplier)
        logo_size = int(180 * size_multiplier)  # حجم متغير للوجو

        # محاولة تحميل اللوجو المحفوظ
        logo_loaded = False
        try:
            # قراءة إعدادات الشركة مباشرة من الملف
            if getattr(sys, 'frozen', False):
                # إذا كان البرنامج مصدر (PyInstaller)
                base_dir = os.path.dirname(sys.executable)
                settings_file = os.path.join(base_dir, "company_settings.json")
            else:
                # إذا كان البرنامج يعمل من الكود المصدري
                settings_file = "company_settings.json"
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    logo_path = settings.get('logo_path', '')
                    logo_base64 = settings.get('logo_base64', '')

                    if logo_base64:
                        # تحميل اللوجو من base64
                        image = QImage()
                        image.loadFromData(base64.b64decode(logo_base64))
                        logo_pixmap = QPixmap.fromImage(image)
                        if not logo_pixmap.isNull():
                            scaled_logo = logo_pixmap.scaled(logo_size, logo_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                            painter.drawPixmap(logo_x, logo_y, scaled_logo)
                            logo_loaded = True
                    elif logo_path and os.path.exists(logo_path):
                        # تحميل اللوجو من المسار
                        logo_pixmap = QPixmap(logo_path)
                        if not logo_pixmap.isNull():
                            scaled_logo = logo_pixmap.scaled(logo_size, logo_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                            painter.drawPixmap(logo_x, logo_y, scaled_logo)
                            logo_loaded = True

        except Exception as e:
            print(f"خطأ في تحميل اللوجو: {e}")

        # إذا لم يتم تحميل اللوجو المخصص، استخدم الافتراضي
        if not logo_loaded:
            try:
                # محاولة تحميل اللوجو الافتراضي
                default_logo_path = resource_path(os.path.join('assets', 'company01_logo.png'))
                if os.path.exists(default_logo_path):
                    logo_pixmap = QPixmap(default_logo_path)
                    if not logo_pixmap.isNull():
                        scaled_logo = logo_pixmap.scaled(logo_size, logo_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                        painter.drawPixmap(logo_x, logo_y, scaled_logo)
                        logo_loaded = True
            except Exception as e:
                print(f"خطأ في تحميل اللوجو الافتراضي: {e}")

        # إذا فشل كل شيء، استخدم النص الافتراضي
        if not logo_loaded:
            painter.setPen(QPen(QColor(0, 0, 0), 2))
            painter.setFont(QFont("Arial", 32, QFont.Bold))
            painter.drawText(logo_x, logo_y + 50, "🏢")
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(logo_x, logo_y + 80, company_name)

        # معلومات الشركة مع مساحة محسوبة ومحسنة مع الارتفاع الجديد
        # تحديد المساحة المتاحة للنص (بعد اللوجو مع مسافة)
        company_start_x = logo_x + logo_size + 40  # بداية النص بعد اللوجو + مسافة
        company_width = (width - 60) - company_start_x  # المساحة المتبقية
        company_x = company_start_x

        painter.setPen(QPen(QColor(0, 0, 0), 2))  # نص أسود

        # رسم اسم الشركة مع خط أكبر
        painter.setFont(QFont("Arial", int(32 * size_multiplier), QFont.Bold))  # حجم خط متغير

        # تقسيم اسم الشركة بناءً على العرض المتاح
        name_lines = []
        words = company_name.split()
        current_line = ""

        for word in words:
            test_line = current_line + " " + word if current_line else word
            # قياس عرض النص الفعلي
            font_metrics = painter.fontMetrics()
            text_width = font_metrics.width(test_line)

            if text_width <= company_width - 30:  # هامش أمان 30px
                current_line = test_line
            else:
                if current_line:
                    name_lines.append(current_line)
                    current_line = word
                else:
                    # إذا كانت الكلمة الواحدة طويلة جداً، قسمها
                    if len(word) > 15:
                        name_lines.append(word[:15] + "...")
                        current_line = ""
                    else:
                        name_lines.append(word)
                        current_line = ""

        if current_line:
            name_lines.append(current_line)

        # رسم اسم الشركة مع إحداثيات صحيحة
        name_start_y = y + int(60 * size_multiplier)  # بداية من داخل الترويسة
        line_height = int(45 * size_multiplier)  # ارتفاع متغير للأسطر

        for i, line in enumerate(name_lines):
            line_y = name_start_y + (i * line_height)
            # التأكد من أن السطر داخل حدود الترويسة
            if line_y <= y + header_height - 30:
                # حساب موضع النص للمحاذاة اليمنى
                font_metrics = painter.fontMetrics()
                text_width = font_metrics.width(line)
                text_x = company_x + company_width - text_width - 10  # محاذاة يمنى مع هامش
                painter.drawText(text_x, line_y, line)

        # تحديد موقع بداية التفاصيل مع مساحة أكبر
        details_start_y = name_start_y + (len(name_lines) * line_height) + 15

        # تفاصيل الشركة مع خط أكبر ومساحة أكبر
        painter.setFont(QFont("Arial", int(18 * size_multiplier)))  # حجم خط متغير
        detail_height = int(45 * size_multiplier)  # ارتفاع متغير للسطر
        current_y = details_start_y

        # حساب المساحة المتبقية في الترويسة الجديدة الأكبر
        remaining_height = (y + header_height - 20) - current_y
        max_lines = max(1, remaining_height // detail_height)

        # العنوان مع إمكانية عرض أكثر
        if company_address and max_lines > 0:
            font_metrics = painter.fontMetrics()

            # تقسيم العنوان إلى أسطر متعددة إذا كان طويل
            if len(company_address) > 50:
                # تقسيم العنوان إلى جزئين
                if ' - ' in company_address:
                    parts = company_address.split(' - ')
                    # السطر الأول
                    first_part = f"📍 {parts[0]}"
                    if current_y <= y + header_height - 40:
                        # محاذاة يمنى للعنوان
                        text_width = font_metrics.width(first_part)
                        text_x = company_x + company_width - text_width - 10
                        painter.drawText(text_x, current_y, first_part)
                        current_y += detail_height
                        max_lines -= 1

                    # السطر الثاني
                    if len(parts) > 1 and max_lines > 0 and current_y <= y + header_height - 40:
                        second_part = f"   {' - '.join(parts[1:])}"
                        if len(second_part) > 50:
                            second_part = second_part[:47] + "..."
                        # محاذاة يمنى للعنوان
                        text_width = font_metrics.width(second_part)
                        text_x = company_x + company_width - text_width - 10
                        painter.drawText(text_x, current_y, second_part)
                        current_y += detail_height
                        max_lines -= 1
                else:
                    # تقسيم العنوان بدون فواصل
                    words = company_address.split()
                    mid = len(words) // 2
                    first_line = f"📍 {' '.join(words[:mid])}"
                    second_line = f"   {' '.join(words[mid:])}"

                    # رسم السطر الأول
                    if current_y <= y + header_height - 40:
                        text_width = font_metrics.width(first_line)
                        text_x = company_x + company_width - text_width - 10
                        painter.drawText(text_x, current_y, first_line)
                        current_y += detail_height
                        max_lines -= 1

                    # رسم السطر الثاني
                    if max_lines > 0 and current_y <= y + header_height - 40:
                        text_width = font_metrics.width(second_line)
                        text_x = company_x + company_width - text_width - 10
                        painter.drawText(text_x, current_y, second_line)
                        current_y += detail_height
                        max_lines -= 1
            else:
                # عنوان قصير
                address_text = f"📍 {company_address}"
                if current_y <= y + header_height - 40:
                    text_width = font_metrics.width(address_text)
                    text_x = company_x + company_width - text_width - 10
                    painter.drawText(text_x, current_y, address_text)
                    current_y += detail_height
                    max_lines -= 1

        # إضافة مسافة إضافية قبل الهاتف
        current_y += 5  # مسافة إضافية

        # الهاتف
        if company_phone:
            phone_text = f"📞 {company_phone}"
            painter.drawText(company_x, current_y, company_width, detail_height, Qt.AlignRight | Qt.AlignVCenter, phone_text)
            current_y += detail_height + 12

        # البريد الإلكتروني
        if company_email:
            email_text = f"📧 {company_email}"
            font_metrics = painter.fontMetrics()
            elided_email = font_metrics.elidedText(email_text, Qt.ElideRight, company_width - 60) # زيادة هامش الأمان
            painter.drawText(company_x, current_y, company_width, detail_height, Qt.AlignRight | Qt.AlignVCenter, elided_email)
            current_y += detail_height + 12

        # الرقم الضريبي
        tax_number = company_settings.get("tax_number", "")
        if tax_number:
            tax_text = f"🏢 ض.ب: {tax_number}"
            font_metrics = painter.fontMetrics()
            elided_tax = font_metrics.elidedText(tax_text, Qt.ElideRight, company_width - 60) # زيادة هامش الأمان
            painter.drawText(company_x, current_y, company_width, detail_height, Qt.AlignRight | Qt.AlignVCenter, elided_tax)
            current_y += detail_height + 12

        # السجل التجاري
        commercial_register = company_settings.get("commercial_register", "")
        if commercial_register:
            commercial_text = f"📄 س.ج: {commercial_register}"
            font_metrics = painter.fontMetrics()
            elided_commercial = font_metrics.elidedText(commercial_text, Qt.ElideRight, company_width - 60) # زيادة هامش الأمان
            painter.drawText(company_x, current_y, company_width, detail_height, Qt.AlignRight | Qt.AlignVCenter, elided_commercial)
            current_y += detail_height + 12

        y += header_height + 20
        
        # عنوان الفاتورة بالأبيض والأسود مع الحفاظ على التصميم
        title_height = 180  # مضاعفة الارتفاع للضعف

        # خلفية بيضاء مع حدود سوداء
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.setPen(QPen(QColor(0, 0, 0), 6))
        painter.drawRoundedRect(15, y, width - 30, title_height, 20, 20)

        # عرض عنوان الفاتورة مع لون مميز للمرتجعات
        if self.invoice_data.get('is_return', False):
            if self.invoice_data.get('is_sale_return', False):
                painter.setPen(QPen(QColor(231, 76, 60), 2))  # أحمر للمرتجع
            else:
                painter.setPen(QPen(QColor(155, 89, 182), 2))  # بنفسجي للمرتجع

        painter.setFont(QFont("Arial", 56, QFont.Bold))  # مضاعفة حجم الخط للضعف

        # عنوان الفاتورة الديناميكي
        title_text = f"🧾 {self.invoice_data.get('invoice_title', 'فاتورة')}"
        painter.drawText(15, y, width - 30, title_height, Qt.AlignCenter, title_text)

        y += title_height + 20

        # رقم الفاتورة/المرتجع ورقم الفاتورة الأصلية
        info_height = 240  # مضاعفة الارتفاع للضعف

        # خلفية بيضاء مع حدود سوداء
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.setPen(QPen(QColor(0, 0, 0), 6))
        painter.drawRoundedRect(10, y, width - 20, info_height, 20, 20)

        painter.setPen(QPen(QColor(44, 62, 80), 4))
        painter.setFont(QFont("Arial", 56, QFont.Bold))  # مضاعفة حجم الخط للضعف

        # رقم الفاتورة/المرتجع
        if self.invoice_data.get('is_return', False):
            number_text = f"رقم المرتجع: R{self.invoice_data['id']:06d}"
        else:
            number_text = f"رقم الفاتورة: #{self.invoice_data['id']:06d}"

        painter.drawText(20, y, width - 40, info_height, Qt.AlignCenter, number_text)
        y += info_height + 30

        # رقم الفاتورة الأصلية للمرتجعات
        if self.invoice_data.get('is_return', False) and self.invoice_data.get('original_invoice_id'):
            original_height = 120  # مضاعفة الارتفاع للضعف

            # خلفية بيضاء مع حدود زرقاء
            painter.setBrush(QBrush(QColor(255, 255, 255)))
            painter.setPen(QPen(QColor(52, 152, 219), 3))
            painter.drawRoundedRect(15, y, width - 30, original_height, 12, 12)

            painter.setPen(QPen(QColor(52, 152, 219), 2))
            painter.setFont(QFont("Arial", 22, QFont.Bold))  # خط أكبر
            original_text = f"الفاتورة الأصلية: #{self.invoice_data['original_invoice_id']:06d}"
            painter.drawText(25, y, width - 50, original_height, Qt.AlignCenter, original_text)
            y += original_height + 15

        # معلومات التاريخ والوقت بحجم أكبر
        date_info_height = 200  # مضاعفة الارتفاع للضعف

        # خلفية بيضاء مع حدود رمادية
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.setPen(QPen(QColor(108, 117, 125), 4))
        painter.drawRoundedRect(10, y, width - 20, date_info_height, 15, 15)

        # تاريخ ووقت الفاتورة
        painter.setPen(QPen(QColor(108, 117, 125), 4))
        painter.setFont(QFont("Arial", 36, QFont.Bold))  # مضاعفة حجم الخط للضعف

        # تقسيم المساحة إلى نصفين
        half_width = (width - 20) // 2

        # التاريخ (يسار)
        date_text = f"📅 التاريخ: {self.invoice_data.get('date', 'غير محدد')}"
        painter.drawText(20, y, half_width, date_info_height, Qt.AlignCenter, date_text)

        # الوقت (يمين)
        time_text = f"🕐 الوقت: {self.invoice_data.get('time', 'غير محدد')}"
        painter.drawText(20 + half_width, y, half_width, date_info_height, Qt.AlignCenter, time_text)

        y += date_info_height + 40  # مسافة إضافية أكبر

        # جدول المنتجات مع تدرج أخضر ومساحة محسوبة
        table_y = y
        table_width = width - 60  # مساحة أكبر للجدول
        table_x = 30

        # رأس الجدول بالأبيض والأسود مع ارتفاع أكبر
        header_height = 160  # مضاعفة الارتفاع للضعف

        # خلفية بيضاء مع حدود سوداء
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.setPen(QPen(QColor(0, 0, 0), 6))
        painter.drawRoundedRect(table_x, table_y, table_width, header_height, 15, 15)

        # عناوين الأعمدة مع توزيع أفضل للمساحة
        painter.setPen(QPen(QColor(0, 0, 0), 4))  # نص أسود أكثر سمكاً
        painter.setFont(QFont("Arial", 44, QFont.Bold))  # مضاعفة حجم الخط للضعف

        # توزيع عرض الأعمدة بشكل أفضل
        col1_width = int(table_width * 0.20)  # الإجمالي - 20%
        col2_width = int(table_width * 0.20)  # الوحدة - 20%
        col3_width = int(table_width * 0.20)  # السعر - 20%
        col4_width = int(table_width * 0.15)  # الكمية - 15%
        col5_width = table_width - col1_width - col2_width - col3_width - col4_width  # المنتج - الباقي

        # رسم عناوين الأعمدة مع مساحة كافية - الترتيب: الإجمالي | الوحدة | السعر | الكمية | المنتج
        painter.drawText(table_x, table_y, col1_width, header_height, Qt.AlignCenter, "الإجمالي")
        painter.drawText(table_x + col1_width, table_y, col2_width, header_height, Qt.AlignCenter, "الوحدة")
        painter.drawText(table_x + col1_width + col2_width, table_y, col3_width, header_height, Qt.AlignCenter, "السعر")
        painter.drawText(table_x + col1_width + col2_width + col3_width, table_y, col4_width, header_height, Qt.AlignCenter, "الكمية")
        painter.drawText(table_x + col1_width + col2_width + col3_width + col4_width, table_y, col5_width, header_height, Qt.AlignCenter, "المنتج")

        table_y += header_height

        # صفوف المنتجات مع ارتفاع أكبر
        painter.setFont(QFont("Arial", 36))  # مضاعفة حجم الخط للضعف
        base_row_height = 140  # مضاعفة الارتفاع للضعف
        item_padding = 30  # مضاعفة الهامش الداخلي للضعف

        for i, item in enumerate(self.invoice_data['items']):
            # --- حساب ارتفاع الصف ديناميكياً ---
            product_name = str(item.get('name', 'غير محدد'))
            font_metrics = painter.fontMetrics()

            # تحديد منطقة اسم المنتج لحساب الارتفاع المطلوب
            product_name_width = col5_width - (2 * item_padding)
            
            # استخدام boundingRect لحساب الارتفاع الفعلي للنص بعد تقسيمه
            required_rect = font_metrics.boundingRect(0, 0, product_name_width, 500, Qt.TextWordWrap, product_name)
            
            # تحديد ارتفاع الصف الحالي
            current_row_height = max(base_row_height, required_rect.height() + (2 * item_padding))

            # --- رسم خلفية الصف ---
            if i % 2 == 0:
                painter.setBrush(QBrush(QColor(245, 245, 245)))
            else:
                painter.setBrush(QBrush(QColor(255, 255, 255)))
            painter.setPen(QPen(QColor(0, 0, 0), 1))
            painter.drawRect(table_x, table_y, table_width, current_row_height)

            # --- رسم محتوى الخلايا مع محاذاة رأسية ---
            painter.setPen(QPen(QColor(0, 0, 0), 1))

            # باقي الأعمدة
            painter.drawText(table_x, table_y, col1_width, current_row_height, Qt.AlignCenter, format_number(float(item.get('total', 0))))
            painter.drawText(table_x + col1_width, table_y, col2_width, current_row_height, Qt.AlignCenter, str(item.get('unit', 'قطعة')))
            painter.drawText(table_x + col1_width + col2_width, table_y, col3_width, current_row_height, Qt.AlignCenter, format_number(float(item.get('price', 0))))
            painter.drawText(table_x + col1_width + col2_width + col3_width, table_y, col4_width, current_row_height, Qt.AlignCenter, str(item.get('quantity', 0)))
            
            # خلية اسم المنتج مع دعم الأسطر المتعددة
            product_name_x = table_x + col1_width + col2_width + col3_width + col4_width
            product_rect = QRect(product_name_x + item_padding, int(table_y), product_name_width, int(current_row_height))
            painter.drawText(product_rect, Qt.TextWordWrap | Qt.AlignLeft | Qt.AlignVCenter, product_name)

            table_y += current_row_height
        
        table_y += 20
        
        # تفاصيل الفاتورة بالأبيض والأسود مع مساحة محسوبة
        details_height = 180

        # خلفية بيضاء مع حدود سوداء
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.setPen(QPen(QColor(0, 0, 0), 2))
        painter.drawRoundedRect(table_x, table_y, table_width, details_height, 12, 12)

        # عنوان التفاصيل مع ارتفاع أكبر
        title_height = 50

        # خلفية بيضاء مع حدود سوداء للعنوان
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.setPen(QPen(QColor(0, 0, 0), 2))
        painter.drawRoundedRect(table_x, table_y, table_width, title_height, 12, 12)

        painter.setPen(QPen(QColor(0, 0, 0), 2))  # نص أسود
        painter.setFont(QFont("Arial", 16, QFont.Bold))
        painter.drawText(table_x, table_y, table_width, title_height, Qt.AlignCenter, "📋 تفاصيل الفاتورة")

        # التفاصيل مع مساحة أكبر وتوزيع أفضل - إضافة حدود واسعة
        details_container_height = 400  # ارتفاع كبير للحاوية

        # رسم حاوية كبيرة للتفاصيل
        painter.setBrush(QBrush(QColor(248, 249, 250)))  # خلفية رمادية فاتحة
        painter.setPen(QPen(QColor(108, 117, 125), 4))
        painter.drawRoundedRect(table_x - 20, table_y + title_height + 20, table_width + 40, details_container_height, 15, 15)

        painter.setPen(QPen(QColor(0, 0, 0), 4))  # نص أسود أكثر سمكاً
        painter.setFont(QFont("Arial", 40))  # مضاعفة حجم الخط للضعف

        detail_y = table_y + title_height + 60  # مسافة أكبر من الحاوية
        left_x = table_x + 20
        right_x = table_x + table_width // 2 + 20

        # الجانب الأيسر - المبالغ المالية
        subtotal = sum(float(item.get('total', 0)) for item in self.invoice_data['items'])
        painter.drawText(left_x, detail_y, 600, 80, Qt.AlignVCenter, f"💰 المجموع الفرعي: {format_number(subtotal)} ج.م")
        painter.drawText(left_x, detail_y + 90, 600, 80, Qt.AlignVCenter, f"💳 المدفوع: {format_number(self.invoice_data['paid_amount'])} ج.م")
        painter.drawText(left_x, detail_y + 180, 600, 80, Qt.AlignVCenter, f"⏳ المتبقي: {format_number(self.invoice_data['remaining_amount'])} ج.م")

        # الجانب الأيمن - معلومات أساسية
        painter.drawText(right_x, detail_y, 600, 80, Qt.AlignVCenter, f"📅 التاريخ: {self.invoice_data['date']}")

        # تحديد تسمية العميل/المورد
        contact_label = "العميل"
        if self.invoice_data.get('is_purchase_return', False) or self.invoice_data.get('invoice_type') == 'مشتريات':
            contact_label = "المورد"

        painter.drawText(right_x, detail_y + 90, 600, 80, Qt.AlignVCenter, f"👤 {contact_label}: {self.invoice_data['customer_name']}")

        # حالة الفاتورة
        status = "مدفوعة" if self.invoice_data['remaining_amount'] == 0 else "جزئية" if self.invoice_data['paid_amount'] > 0 else "غير مدفوعة"
        status_icon = "✅" if status == "مدفوعة" else "⚠️" if status == "جزئية" else "❌"
        painter.drawText(right_x, detail_y + 180, 600, 80, Qt.AlignVCenter, f"{status_icon} الحالة: {status}")

        # معلومات إضافية إذا وجدت
        extra_y = detail_y + 270  # مسافة أكبر
        if self.invoice_data['discount'] > 0:
            painter.drawText(left_x, extra_y, 600, 80, Qt.AlignVCenter, f"🎯 الخصم: {format_currency(self.invoice_data['discount'])} ج.م")

        # الإجمالي النهائي منفصل عن قسم التفاصيل
        total_y = table_y + details_height + details_container_height + 60  # مسافة كافية بعد قسم التفاصيل

        # خلفية بيضاء مع حدود سوداء سميكة
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.setPen(QPen(QColor(0, 0, 0), 8))  # حدود سوداء سميكة جداً
        painter.drawRoundedRect(table_x - 10, total_y, table_width + 20, 160, 20, 20)  # مضاعفة الارتفاع للضعف

        painter.setPen(QPen(QColor(0, 0, 0), 4))  # نص أسود أكثر سمكاً
        painter.setFont(QFont("Arial", 52, QFont.Bold))  # مضاعفة حجم الخط للضعف
        painter.drawText(table_x - 10, total_y, table_width + 20, 160, Qt.AlignCenter, f"💎 الإجمالي النهائي: {self.invoice_data['total_amount']:,.0f} ج.م")

        return total_y + 200  # مسافة كافية بعد الإجمالي

    def draw_roll_invoice(self, painter, width, height, company_name, company_phone):
        """رسم فاتورة رول بتصميم محسن مع جميع التفاصيل"""
        y = 10

        # تحميل إعدادات الشركة الكاملة
        company_settings = get_company_settings()
        company_address = company_settings.get("address", "حي الصفا - جدة - المملكة العربية السعودية")
        company_email = company_settings.get("email", "<EMAIL>")

        # ترويسة محسنة بالأبيض والأسود مع ارتفاع أكبر (بدون حدود)
        header_height = 120  # ارتفاع أكبر للترويسة

        # رسم اللوجو المخصص أو الافتراضي (حجم أصغر للرول)
        logo_x = 10
        logo_y = y + 10
        logo_size = 60  # حجم مناسب للرول

        # محاولة تحميل اللوجو المحفوظ
        logo_loaded = False
        try:
            # قراءة إعدادات الشركة مباشرة من الملف
            if getattr(sys, 'frozen', False):
                # إذا كان البرنامج مصدر (PyInstaller)
                base_dir = os.path.dirname(sys.executable)
                settings_file = os.path.join(base_dir, "company_settings.json")
            else:
                # إذا كان البرنامج يعمل من الكود المصدري
                settings_file = "company_settings.json"
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    logo_path = settings.get('logo_path', '')
                    logo_base64 = settings.get('logo_base64', '')

                    if logo_base64:
                        # تحميل اللوجو من base64
                        image = QImage()
                        image.loadFromData(base64.b64decode(logo_base64))
                        logo_pixmap = QPixmap.fromImage(image)
                        if not logo_pixmap.isNull():
                            scaled_logo = logo_pixmap.scaled(logo_size, logo_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                            painter.drawPixmap(logo_x, logo_y, scaled_logo)
                            logo_loaded = True
                    elif logo_path and os.path.exists(logo_path):
                        # تحميل اللوجو من المسار
                        logo_pixmap = QPixmap(logo_path)
                        if not logo_pixmap.isNull():
                            scaled_logo = logo_pixmap.scaled(logo_size, logo_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                            painter.drawPixmap(logo_x, logo_y, scaled_logo)
                            logo_loaded = True

        except Exception as e:
            print(f"خطأ في تحميل اللوجو: {e}")

        # إذا لم يتم تحميل اللوجو المخصص، استخدم الافتراضي
        if not logo_loaded:
            try:
                # محاولة تحميل اللوجو الافتراضي
                default_logo_path = resource_path(os.path.join('assets', 'company01_logo.png'))
                if os.path.exists(default_logo_path):
                    logo_pixmap = QPixmap(default_logo_path)
                    if not logo_pixmap.isNull():
                        scaled_logo = logo_pixmap.scaled(logo_size, logo_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                        painter.drawPixmap(logo_x, logo_y, scaled_logo)
                        logo_loaded = True
            except Exception as e:
                print(f"خطأ في تحميل اللوجو الافتراضي: {e}")

        # إذا فشل كل شيء، استخدم النص الافتراضي
        if not logo_loaded:
            painter.setPen(QPen(QColor(0, 0, 0), 2))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(logo_x, logo_y + 25, "🏢")
            painter.setFont(QFont("Arial", 10, QFont.Bold))
            painter.drawText(logo_x, logo_y + 40, company_name)

        # معلومات الشركة في الرول (بجانب اللوجو)
        company_start_x = logo_x + logo_size + 15  # بداية النص بعد اللوجو
        company_width = width - company_start_x - 10  # المساحة المتبقية

        painter.setPen(QPen(QColor(0, 0, 0), 2))  # نص أسود

        # اسم الشركة مع خط مناسب للرول
        painter.setFont(QFont("Arial", 11, QFont.Bold))

        # تقسيم اسم الشركة للرول
        name_lines = []
        words = company_name.split()
        current_line = ""

        for word in words:
            test_line = current_line + " " + word if current_line else word
            font_metrics = painter.fontMetrics()
            text_width = font_metrics.width(test_line)

            if text_width <= company_width - 10:  # هامش أمان للرول
                current_line = test_line
            else:
                if current_line:
                    name_lines.append(current_line)
                    current_line = word
                else:
                    # تقصير الكلمة الطويلة للرول
                    if len(word) > 12:
                        name_lines.append(word[:10] + "...")
                        current_line = ""
                    else:
                        name_lines.append(word)
                        current_line = ""

        if current_line:
            name_lines.append(current_line)

        # رسم اسم الشركة مع مسافات أفضل
        name_start_y = y + 15  # بداية صحيحة من أعلى الترويسة
        line_height = 16  # مسافة أكبر بين الأسطر

        for i, line in enumerate(name_lines):
            line_y = name_start_y + (i * line_height)
            painter.drawText(company_start_x, line_y, line)

        # مسافة بين اسم الشركة والتفاصيل
        details_gap = 8  # مسافة إضافية

        # تفاصيل الشركة مع خط أصغر للرول ومسافات أفضل
        painter.setFont(QFont("Arial", 9))  # خط أكبر قليلاً
        detail_height = 14  # مسافة أكبر بين التفاصيل
        current_y = name_start_y + (len(name_lines) * line_height) + details_gap

        # العنوان مع تقسيم ذكي للرول
        if company_address:
            # تقسيم العنوان إلى أسطر متعددة
            address_words = company_address.split()
            address_lines = []
            current_address_line = ""

            for word in address_words:
                test_line = current_address_line + " " + word if current_address_line else word
                # حساب عرض النص مع الأيقونة
                test_with_icon = f"📍 {test_line}"
                font_metrics = painter.fontMetrics()
                text_width = font_metrics.width(test_with_icon)

                if text_width <= company_width - 5:  # هامش أمان
                    current_address_line = test_line
                else:
                    if current_address_line:
                        address_lines.append(current_address_line)
                        current_address_line = word
                    else:
                        # تقصير الكلمة الطويلة
                        if len(word) > 15:
                            address_lines.append(word[:12] + "...")
                            current_address_line = ""
                        else:
                            address_lines.append(word)
                            current_address_line = ""

            if current_address_line:
                address_lines.append(current_address_line)

            # رسم العنوان على أسطر متعددة
            for i, line in enumerate(address_lines):
                if i == 0:
                    address_text = f"📍 {line}"
                else:
                    address_text = f"   {line}"  # مسافة بدلاً من الأيقونة
                painter.drawText(company_start_x, current_y, address_text)
                current_y += detail_height  # مسافة بين الأسطر

            current_y += 2  # مسافة إضافية بعد العنوان

        # الهاتف مع مسافة
        if company_phone:
            phone_text = f"📞 {company_phone}"
            painter.drawText(company_start_x, current_y, phone_text)
            current_y += detail_height + 2  # مسافة إضافية

        # البريد الإلكتروني مختصر مع مسافة
        if company_email:
            # تقصير البريد للرول
            if len(company_email) > 18:
                short_email = company_email[:15] + "..."
            else:
                short_email = company_email
            email_text = f"📧 {short_email}"
            painter.drawText(company_start_x, current_y, email_text)
            current_y += detail_height + 2  # مسافة إضافية

        # الرقم الضريبي والسجل التجاري للرول مع مسافة
        try:
            tax_number = company_settings.get("tax_number", "310123456789003")
            commercial_register = company_settings.get("commercial_register", "")

            # الرقم الضريبي
            if tax_number:
                tax_text = f"🏢 {tax_number}"
                painter.drawText(company_start_x, current_y, tax_text)
                current_y += detail_height + 2

            # السجل التجاري
            if commercial_register:
                commercial_text = f"📄 {commercial_register}"
                painter.drawText(company_start_x, current_y, commercial_text)

        except Exception as e:
            tax_text = "🏢 310..."
            painter.drawText(company_start_x, current_y, tax_text)

        # مسافة كبيرة بين الترويسة ومعلومات الفاتورة
        y += header_height + 20  # مسافة كافية بعد الترويسة

        # عنوان الفاتورة (مرتجع أو عادية)
        painter.setPen(QPen(QColor(44, 62, 80), 1))
        painter.setFont(QFont("Arial", 12, QFont.Bold))

        # عرض عنوان الفاتورة مع لون مميز للمرتجعات
        if self.invoice_data.get('is_return', False):
            if self.invoice_data.get('is_sale_return', False):
                painter.setPen(QPen(QColor(231, 76, 60), 2))  # أحمر للمرتجع
            else:
                painter.setPen(QPen(QColor(155, 89, 182), 2))  # بنفسجي للمرتجع

        painter.drawText(5, y, width - 10, 20, Qt.AlignCenter, self.invoice_data.get('invoice_title', 'فاتورة'))
        y += 30

        # رقم الفاتورة/المرتجع
        painter.setPen(QPen(QColor(44, 62, 80), 1))
        painter.setFont(QFont("Arial", 10, QFont.Bold))

        if self.invoice_data.get('is_return', False):
            painter.drawText(5, y, width - 10, 20, Qt.AlignCenter, f"رقم المرتجع: R{self.invoice_data['id']:06d}")
        else:
            painter.drawText(5, y, width - 10, 20, Qt.AlignCenter, f"رقم الفاتورة: #{self.invoice_data['id']:06d}")
        y += 25

        # رقم الفاتورة الأصلية للمرتجعات
        if self.invoice_data.get('is_return', False) and self.invoice_data.get('original_invoice_id'):
            painter.setPen(QPen(QColor(52, 152, 219), 1))
            painter.drawText(5, y, width - 10, 20, Qt.AlignCenter, f"الفاتورة الأصلية: #{self.invoice_data['original_invoice_id']:06d}")
            y += 25

        painter.setPen(QPen(QColor(44, 62, 80), 1))
        painter.drawText(5, y, width - 10, 20, Qt.AlignCenter, f"التاريخ: {self.invoice_data['date']}")
        y += 25

        # تحديد تسمية العميل/المورد
        contact_label = "العميل"
        if self.invoice_data.get('is_purchase_return', False) or self.invoice_data.get('invoice_type') == 'مشتريات':
            contact_label = "المورد"

        painter.drawText(5, y, width - 10, 20, Qt.AlignCenter, f"{contact_label}: {self.invoice_data['customer_name']}")
        y += 35

        # خط فاصل مع مسافات أكبر
        painter.setPen(QPen(QColor(189, 195, 199), 2))
        painter.drawLine(5, y, width - 5, y)
        y += 25  # مسافة أكبر بعد الخط الفاصل

        # المنتجات مع مسافات محسنة
        painter.setFont(QFont("Arial", 9))
        painter.setPen(QPen(QColor(44, 62, 80), 1))
        line_padding = 4  # مسافة بين الأسطر

        for i, item in enumerate(self.invoice_data['items']):
            font_metrics = painter.fontMetrics()
            line_height = font_metrics.height() + line_padding

            # تفاصيل السطر: الإجمالي = السعر × الكمية (محاذاة يسار)
            details_text = f"{format_number(float(item.get('total', 0)))} = {format_number(float(item.get('price', 0)))} × {item.get('quantity', 0)}"
            
            # اسم المنتج
            product_name = str(item.get('name', 'غير محدد'))

            # حساب الأسطر المطلوبة لاسم المنتج
            product_rect = font_metrics.boundingRect(0, 0, width // 2, 500, Qt.TextWordWrap, product_name)
            
            # حساب الارتفاع الإجمالي لهذا العنصر
            item_height = max(line_height, product_rect.height())

            # رسم تفاصيل السعر والكمية
            painter.drawText(5, y, width - 10, int(item_height), Qt.AlignLeft | Qt.AlignVCenter, details_text)

            # رسم اسم المنتج على أسطر متعددة
            product_draw_rect = QRect(width // 2 - 5, y, width // 2, int(item_height))
            painter.drawText(product_draw_rect, Qt.TextWordWrap | Qt.AlignRight | Qt.AlignVCenter, product_name)
            
            y += item_height + line_padding # زيادة y بناءً على الارتفاع الفعلي

        # مسافة كبيرة قبل الخط الفاصل
        y += 20  # مسافة أكبر

        # خط فاصل
        painter.setPen(QPen(QColor(189, 195, 199), 2))
        painter.drawLine(5, y, width - 5, y)
        y += 25  # مسافة أكبر بعد الخط الفاصل

        # قسم تفاصيل الفاتورة للرول (بدون حدود)
        details_height = 120  # ارتفاع مناسب للرول

        # بدون خلفية أو حدود - فقط المحتوى

        # عنوان التفاصيل
        title_height = 25
        painter.setFont(QFont("Arial", 10, QFont.Bold))
        painter.setPen(QPen(QColor(0, 0, 0), 2))
        painter.drawText(5, y + 5, width - 10, title_height, Qt.AlignCenter, "📋 تفاصيل الفاتورة")

        # التفاصيل
        painter.setFont(QFont("Arial", 8))
        detail_y = y + title_height + 10

        # حساب المجموع الفرعي
        subtotal = sum(float(item.get('total', 0)) for item in self.invoice_data['items'])

        # حالة الفاتورة
        status = "مدفوعة" if self.invoice_data['remaining_amount'] == 0 else "جزئية" if self.invoice_data['paid_amount'] > 0 else "غير مدفوعة"
        status_icon = "✅" if status == "مدفوعة" else "⚠️" if status == "جزئية" else "❌"

        # عرض التفاصيل في سطور (محاذاة يمين)
        painter.drawText(10, detail_y, width - 20, 15, Qt.AlignRight | Qt.AlignVCenter, f"💰 المجموع الفرعي: {subtotal:,.0f} ج.م")
        detail_y += 15

        painter.drawText(10, detail_y, width - 20, 15, Qt.AlignRight | Qt.AlignVCenter, f"💳 المدفوع: {self.invoice_data['paid_amount']:,.0f} ج.م")
        detail_y += 15

        painter.drawText(10, detail_y, width - 20, 15, Qt.AlignRight | Qt.AlignVCenter, f"⏳ المتبقي: {self.invoice_data['remaining_amount']:,.0f} ج.م")
        detail_y += 15

        painter.drawText(10, detail_y, width - 20, 15, Qt.AlignRight | Qt.AlignVCenter, f"{status_icon} الحالة: {status}")
        detail_y += 15

        # الخصم إذا وجد (محاذاة يمين)
        if self.invoice_data['discount'] > 0:
            painter.drawText(10, detail_y, width - 20, 15, Qt.AlignRight | Qt.AlignVCenter, f"🎯 الخصم: {self.invoice_data['discount']:,.0f} ج.م")

        y += details_height + 15  # مسافة بعد قسم التفاصيل

        # الإجمالي النهائي بدون حدود للرول
        painter.setFont(QFont("Arial", 12, QFont.Bold))  # خط أكبر قليلاً
        painter.setPen(QPen(QColor(0, 0, 0), 2))  # نص أسود غامق

        # نص الإجمالي بدون خلفية أو حدود
        painter.drawText(5, y + 10, width - 10, 25, Qt.AlignCenter, f"💎 الإجمالي النهائي: {self.invoice_data['total_amount']:,.0f} ج.م")

        return y + 40  # مسافة إضافية في النهاية

    def save_as_pdf(self):
        """حفظ الفاتورة كملف PDF بجودة عالية"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            filename, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ الفاتورة كـ PDF",
                f"فاتورة_{self.invoice_data['id']:06d}.pdf",
                "PDF Files (*.pdf)"
            )

            if filename:
                # إعداد الطابعة بدقة عالية
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(filename)
                printer.setColorMode(QPrinter.Color)  # تفعيل الألوان

                if self.printer_type == "a4":
                    printer.setPageSize(QPrinter.A4)
                    printer.setOrientation(QPrinter.Portrait)  # تأكيد الوضع الطولي
                    printer.setPageMargins(0, 0, 0, 0, QPrinter.Millimeter)  # بدون هوامش لملء الصفحة
                    # استخدام نفس الأبعاد المستخدمة في المعاينة
                    pdf_width = 1000 * 3  # نفس الأبعاد مع جودة أعلى
                    pdf_height = 1414 * 3  # نفس الأبعاد مع جودة أعلى
                else:
                    # إعداد حجم الرول بطريقة صحيحة
                    from PyQt5.QtCore import QSizeF
                    printer.setPageSize(QPrinter.Custom)
                    # تحديد حجم الورق بالمليمتر (80mm عرض، 200mm طول)
                    paper_size = QSizeF(80, 200)  # عرض × طول بالمليمتر
                    printer.setPaperSize(paper_size, QPrinter.Millimeter)
                    pdf_width = 945
                    pdf_height = 2362

                # إنشاء صورة بدقة عالية
                pixmap = QPixmap(pdf_width, pdf_height)
                pixmap.fill(QColor(255, 255, 255))  # خلفية بيضاء

                # رسم الفاتورة على الصورة
                painter = QPainter(pixmap)
                # تعيين وضع الطباعة
                self.is_preview_mode = False
                self.draw_invoice(painter, pdf_width, pdf_height)
                # إعادة تعيين وضع المعاينة
                self.is_preview_mode = True
                painter.end()

                # طباعة الصورة على PDF
                pdf_painter = QPainter()
                pdf_painter.begin(printer)

                # ملء الصفحة بالكامل مثل المعاينة تماماً
                page_rect = printer.pageRect()

                # رسم الصورة لتملأ الصفحة بالكامل
                pdf_painter.drawPixmap(0, 0, page_rect.width(), page_rect.height(), pixmap)
                pdf_painter.end()

                QMessageBox.information(self, "نجح", f"تم حفظ الفاتورة بنجاح في:\n{filename}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ PDF:\n{str(e)}")

    def print_invoice(self):
        """طباعة الفاتورة بجودة عالية"""
        try:
            printer = QPrinter(QPrinter.HighResolution)
            printer.setColorMode(QPrinter.Color)  # تفعيل الألوان

            if self.printer_type == "a4":
                printer.setPageSize(QPrinter.A4)
                printer.setOrientation(QPrinter.Portrait)  # تأكيد الوضع الطولي
                printer.setPageMargins(0, 0, 0, 0, QPrinter.Millimeter)  # بدون هوامش لملء الصفحة
                print_width = 1000 * 3  # نفس الأبعاد المستخدمة في المعاينة
                print_height = 1414 * 3  # نفس الأبعاد المستخدمة في المعاينة
            else:
                # إعداد حجم الرول للطباعة بطريقة صحيحة
                from PyQt5.QtCore import QSizeF
                printer.setPageSize(QPrinter.Custom)
                # تحديد حجم الورق بالمليمتر (80mm عرض، 200mm طول)
                paper_size = QSizeF(80, 200)  # عرض × طول بالمليمتر
                printer.setPaperSize(paper_size, QPrinter.Millimeter)
                print_width = 945
                print_height = 2362

            print_dialog = QPrintDialog(printer, self)
            if print_dialog.exec_() == QPrintDialog.Accepted:
                # إنشاء صورة بدقة عالية للطباعة
                pixmap = QPixmap(print_width, print_height)
                pixmap.fill(QColor(255, 255, 255))

                # رسم الفاتورة على الصورة
                painter = QPainter(pixmap)
                # تعيين وضع الطباعة
                self.is_preview_mode = False
                self.draw_invoice(painter, print_width, print_height)
                # إعادة تعيين وضع المعاينة
                self.is_preview_mode = True
                painter.end()

                # طباعة الصورة
                print_painter = QPainter()
                print_painter.begin(printer)

                # ملء الصفحة بالكامل مثل المعاينة تماماً
                page_rect = printer.pageRect()

                # رسم الصورة لتملأ الصفحة بالكامل
                print_painter.drawPixmap(0, 0, page_rect.width(), page_rect.height(), pixmap)
                print_painter.end()

                QMessageBox.information(self, "نجح", "تم إرسال الفاتورة للطباعة بنجاح!")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الطباعة:\n{str(e)}")

    def draw_new_preview_design(self, painter, width, height, company_name, company_address, company_phone, company_email):
        """رسم التصميم الجديد للمعاينة فقط - بناءً على الصورة المرسلة - مكبر ضعفين"""
        from utils.company_settings import get_company_settings
        company_settings = get_company_settings()
        y = 40  # مضاعف

        # إعدادات الخط والألوان للتصميم الجديد
        painter.setRenderHint(QPainter.Antialiasing)

        # 1. ترويسة الشركة - مستطيل مع حدود مدورة (مطابق للصورة) - بدون هوامش
        header_height = 360  # مضاعف
        painter.setBrush(QBrush(QColor(255, 255, 255)))  # خلفية بيضاء
        painter.setPen(QPen(QColor(0, 0, 0), 4))  # حدود سوداء مضاعفة
        painter.drawRoundedRect(0, y, width, header_height, 30, 30)  # بدون هوامش - يصل للحدود

        # اسم الشركة في المنتصف (أكبر وأوضح) - بدون هوامش
        painter.setPen(QPen(QColor(0, 0, 0), 4))
        painter.setFont(QFont("Arial", 40, QFont.Bold))  # مضاعف
        painter.drawText(0, y + 30, width, 80, Qt.AlignCenter, "شركة المحاسبة العصرية")  # بدون هوامش

        # العنوان الرئيسي للشركة مع الأيقونة - بدون هوامش
        painter.setFont(QFont("Arial", 28))  # مضاعف
        painter.drawText(0, y + 110, width, 50, Qt.AlignCenter, "العنوان الرئيسي للشركة 💗")  # بدون هوامش

        # معلومات الاتصال في صفوف (مطابق للصورة) - بدون هوامش
        painter.setFont(QFont("Arial", 24))  # مضاعف
        contact_y = y + 170  # مضاعف

        # الهاتف مع الأيقونة الحمراء
        phone_text = f"📞 {company_phone}"
        painter.drawText(0, contact_y, width, 40, Qt.AlignCenter, phone_text)  # بدون هوامش

        # البريد الإلكتروني مع الأيقونة الزرقاء
        email_text = f"📧 {company_email}"
        painter.drawText(0, contact_y + 44, width, 40, Qt.AlignCenter, email_text)  # بدون هوامش

        # الرقم الضريبي والسجل التجاري
        tax_number = company_settings.get("tax_number", "00000000000000")
        commercial_register = company_settings.get("commercial_register", "00000000000000")

        painter.drawText(0, contact_y + 88, width, 40, Qt.AlignCenter, f"ض.ب: {tax_number} 📄")  # بدون هوامش
        painter.drawText(0, contact_y + 132, width, 40, Qt.AlignCenter, f"س.ج: {commercial_register} 📄")  # بدون هوامش

        y += header_height + 30  # مضاعف

        # 2. عنوان الفاتورة (مطابق للصورة) - بدون هوامش
        title_height = 100  # مضاعف
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.setPen(QPen(QColor(0, 0, 0), 4))  # مضاعف
        painter.drawRoundedRect(0, y, width, title_height, 20, 20)  # بدون هوامش

        painter.setFont(QFont("Arial", 32, QFont.Bold))  # مضاعف
        title_text = f"فاتورة مبيعات 📄"
        painter.drawText(0, y, width, title_height, Qt.AlignCenter, title_text)

        y += title_height + 30  # مضاعف

        # 3. رقم الفاتورة (مطابق للصورة) - بدون هوامش
        number_height = 90  # مضاعف
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.setPen(QPen(QColor(0, 0, 0), 4))  # مضاعف
        painter.drawRoundedRect(0, y, width, number_height, 20, 20)  # بدون هوامش

        painter.setFont(QFont("Arial", 28, QFont.Bold))  # مضاعف
        number_text = f"رقم الفاتورة: #{self.invoice_data['id']:06d}"
        painter.drawText(0, y, width, number_height, Qt.AlignCenter, number_text)

        y += number_height + 30  # مضاعف

        # 4. جدول المنتجات
        y = self.draw_new_products_table(painter, width, y)

        # 5. تفاصيل الفاتورة
        y = self.draw_new_invoice_details(painter, width, y)

        # 6. الإجمالي النهائي
        self.draw_new_final_total(painter, width, y)

    def draw_new_products_table(self, painter, width, start_y):
        """رسم جدول المنتجات بالتصميم الجديد (مطابق للصورة) - بدون هوامش"""
        y = start_y
        table_width = width  # بدون هوامش
        table_x = 0  # بدون هوامش

        # رأس الجدول (مطابق للصورة) - بدون هوامش
        header_height = 100  # مضاعف
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.setPen(QPen(QColor(0, 0, 0), 4))  # مضاعف
        painter.drawRoundedRect(table_x, y, table_width, header_height, 16, 16)  # بدون هوامش

        # عناوين الأعمدة بالترتيب الجديد: الإجمالي | الوحدة | السعر | الكمية | المنتج - مع توزيع أفضل للمساحة
        painter.setFont(QFont("Arial", 28, QFont.Bold))  # مضاعف
        painter.setPen(QPen(QColor(0, 0, 0), 4))  # مضاعف

        # توزيع أفضل للأعمدة - المنتج يأخذ مساحة كبيرة جداً
        col1_width = int(table_width * 0.12)  # الإجمالي - 12%
        col2_width = int(table_width * 0.12)  # الوحدة - 12%
        col3_width = int(table_width * 0.12)  # السعر - 12%
        col4_width = int(table_width * 0.12)  # الكمية - 12%
        col5_width = table_width - col1_width - col2_width - col3_width - col4_width  # المنتج - 52%

        headers = ["الإجمالي", "الوحدة", "السعر", "الكمية", "المنتج"]
        col_widths = [col1_width, col2_width, col3_width, col4_width, col5_width]

        x_pos = table_x
        for i, (header, width) in enumerate(zip(headers, col_widths)):
            painter.drawText(x_pos, y, width, header_height, Qt.AlignCenter, header)
            x_pos += width

        y += header_height

        # صفوف المنتجات (مطابق للصورة) - مكبر ضعفين
        painter.setFont(QFont("Arial", 24))  # مضاعف
        row_height = 90  # مضاعف

        for i, item in enumerate(self.invoice_data['items']):
            # خلفية متناوبة (أبيض ورمادي فاتح)
            if i % 2 == 0:
                painter.setBrush(QBrush(QColor(250, 250, 250)))
            else:
                painter.setBrush(QBrush(QColor(255, 255, 255)))

            painter.setPen(QPen(QColor(0, 0, 0), 2))  # مضاعف
            painter.drawRect(table_x, y, table_width, row_height)

            # بيانات الصف مع التوزيع الجديد للأعمدة
            painter.setPen(QPen(QColor(0, 0, 0), 2))  # مضاعف

            # الإجمالي
            total_text = f"{float(item.get('total', 0)):,.0f}"
            painter.drawText(table_x, y, col1_width, row_height, Qt.AlignCenter, total_text)

            # الوحدة
            unit_text = str(item.get('unit', 'قطعة'))
            painter.drawText(table_x + col1_width, y, col2_width, row_height, Qt.AlignCenter, unit_text)

            # السعر
            price_text = f"{float(item.get('price', 0)):,.0f}"
            painter.drawText(table_x + col1_width + col2_width, y, col3_width, row_height, Qt.AlignCenter, price_text)

            # الكمية
            qty_text = f"{float(item.get('quantity', 0)):,.1f}"
            painter.drawText(table_x + col1_width + col2_width + col3_width, y, col4_width, row_height, Qt.AlignCenter, qty_text)

            # المنتج - مع مساحة كبيرة جداً (52% من العرض)
            product_name = str(item.get('name', 'غير محدد'))
            # زيادة الحد الأقصى للنص لأن المساحة كبيرة جداً الآن
            if len(product_name) > 50:
                product_name = product_name[:47] + "..."
            painter.drawText(table_x + col1_width + col2_width + col3_width + col4_width, y, col5_width, row_height, Qt.AlignCenter, product_name)

            y += row_height

        return y + 30  # مضاعف

    def draw_new_invoice_details(self, painter, width, start_y):
        """رسم تفاصيل الفاتورة بالتصميم الجديد (مطابق للصورة) - بدون هوامش"""
        y = start_y
        details_width = width  # بدون هوامش
        details_x = 0  # بدون هوامش
        details_height = 320  # مضاعف

        # إطار التفاصيل - بدون هوامش
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.setPen(QPen(QColor(0, 0, 0), 4))  # مضاعف
        painter.drawRoundedRect(details_x, y, details_width, details_height, 16, 16)  # بدون هوامش

        # عنوان التفاصيل - مع مساحة أوسع
        painter.setFont(QFont("Arial", 28, QFont.Bold))  # مضاعف
        painter.setPen(QPen(QColor(0, 0, 0), 4))  # مضاعف
        painter.drawText(details_x, y + 20, details_width, 60, Qt.AlignCenter, "تفاصيل الفاتورة 📋")  # مضاعف

        # تقسيم المساحة إلى عمودين
        col_width = details_width // 2
        detail_y = y + 90  # مضاعف
        painter.setFont(QFont("Arial", 24))  # مضاعف

        # العمود الأيسر - المبالغ
        subtotal = sum(float(item.get('total', 0)) for item in self.invoice_data['items'])

        left_details = [
            f"💰 المجموع الفرعي: {subtotal:,.0f} ج.م",
            f"💳 المدفوع: {self.invoice_data['paid_amount']:,.0f} ج.م",
            f"⏳ المتبقي: {self.invoice_data['remaining_amount']:,.0f} ج.م"
        ]

        for i, detail in enumerate(left_details):
            painter.drawText(details_x + 30, detail_y + i*56, col_width - 60, 50, Qt.AlignLeft | Qt.AlignVCenter, detail)  # مضاعف

        # العمود الأيمن - معلومات أساسية
        right_details = [
            f"📅 التاريخ: {self.invoice_data['date']}",
            f"👤 العميل: {self.invoice_data['customer_name']}",
            f"✅ الحالة: مدفوعة" if self.invoice_data['remaining_amount'] == 0 else "⚠️ الحالة: جزئية" if self.invoice_data['paid_amount'] > 0 else "❌ الحالة: غير مدفوعة"
        ]

        for i, detail in enumerate(right_details):
            painter.drawText(details_x + col_width + 30, detail_y + i*56, col_width - 60, 50, Qt.AlignRight | Qt.AlignVCenter, detail)  # مضاعف

        return y + details_height + 30  # مضاعف

    def draw_new_final_total(self, painter, width, start_y):
        """رسم الإجمالي النهائي بالتصميم الجديد (مطابق للصورة) - بدون هوامش"""
        y = start_y
        total_width = width  # بدون هوامش
        total_x = 0  # بدون هوامش
        total_height = 120  # مضاعف

        # إطار الإجمالي مع تأثير الماس (مطابق للصورة) - بدون هوامش
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.setPen(QPen(QColor(0, 0, 0), 6))  # مضاعف
        painter.drawRoundedRect(total_x, y, total_width, total_height, 24, 24)  # بدون هوامش

        # النص مع أيقونة الماس - بدون هوامش
        painter.setFont(QFont("Arial", 36, QFont.Bold))  # مضاعف
        painter.setPen(QPen(QColor(0, 0, 0), 4))  # مضاعف
        total_text = f"💎 الإجمالي النهائي: {self.invoice_data['total_amount']:,.0f} ج.م"
        painter.drawText(total_x, y, total_width, total_height, Qt.AlignCenter, total_text)


def show_advanced_print_dialog(engine, invoice_id, parent=None):
    """عرض نافذة الطباعة المتقدمة"""
    dialog = AdvancedInvoicePrinter(engine, invoice_id, parent)
    dialog.exec_()
